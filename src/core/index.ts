import type { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import type { IdeCommonMessage, IMessenger } from '@/protocol/messenger';

import { SearchManager } from '@/indexing/SearchManager';
import { AGENT_NAMESPACE } from '@/util/const';

import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { IdeStateManager } from './IdeStateManager';
import { SqliteDb } from '@/db/sqlite';
import { BaseAgentManager } from '@/agent/AgentManager';
import { STATUS } from '@/protocol/index.d';
import { AgentMessageHandler } from './agent-message-handler';
import { McpClient } from '@/mcp';
import { getKwaipilotIgnoreFiles } from '@/util/file';
import ignore from 'ignore';
import { migrate } from '@/db/sqlite/migrate';
import { getRulesList, reportUserRules } from '@/agent/rules';
import { getDiffSet } from '@/agent/tools/checkpoints/checkpoint';
import { CloudIndexManager } from '@/index-manager/CloudIndexManager';
import { Api } from '@/http';
import { initTraceConfig } from '@/util/langfuse';
import { UIPreviewManager } from '@/ui-preview';
import { DeployServerManager } from '@/deploy-server';
import { checkProjectWiki, deleteProjectWiki, getWikiList, initProjectWiki, cancelGenerate } from '@/project-wiki';

export class Core {
  invoke<T extends keyof ToCoreProtocol>(messageType: T, data: ToCoreProtocol[T][0]): ToCoreProtocol[T][1] {
    return this.messenger.invoke(messageType, data);
  }

  send<T extends keyof FromCoreProtocol>(messageType: T, data: FromCoreProtocol[T][0], messageId?: string): string {
    return this.messenger.send(messageType, data, messageId);
  }
  private logger = new Logger('Core');
  private cloudIndexManager: CloudIndexManager;
  private agentMessageHandler: AgentMessageHandler;
  private ideStateManager: IdeStateManager;
  private httpClient = new Api();

  constructor(private readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    // Ensure .kwaipilot directory is created
    this.logger.info('start init core');
    this.cloudIndexManager = new CloudIndexManager(this.messenger);
    this.logger.info('IndexManager initialized');
    this.ideStateManager = new IdeStateManager(this.messenger);
    this.logger.info('IdeStateManager initialized');
    this.agentMessageHandler = new AgentMessageHandler(this.messenger);
    this.logger.info('AgentMessageHandler initialized');
    GlobalConfig.init(this.messenger);
    this.logger.info('GlobalConfig initialized');
    this.initMcpAndUIPreview();
    this.logger.info('initMcp initialized');
    this.logger.info('after initMcp;----');

    this.registerListeners();
    this.logger.info('registerListeners initialized');
  }

  // 提供agentManager的getter，保持与以前相同的API
  get agentManager(): BaseAgentManager | undefined {
    return this.agentMessageHandler.agentManager;
  }

  async init() {
    if (process.env.NODE_ENV === 'test') {
      await GlobalConfig.getConfig().initData();
      await this.defaultInit();
      return;
    }
    const startTime = Date.now();
    const maxRetries = 3;
    const timeoutMs = 5 * 1000; // 10 second timeout

    let attempt = 0;
    let success = false;
    this.logger.info(`系统初始化开始： 第 ${attempt} 次尝试`);
    while (!success && attempt < maxRetries) {
      attempt++;
      try {
        // Create a timeout promise that rejects after timeoutMs
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(
            () => reject(new Error(`Operation timed out after ${timeoutMs}ms`)),
            timeoutMs * (attempt === 1 ? 1 : attempt * 2)
          );
        });

        // Race between the actual operation and the timeout
        await Promise.race([GlobalConfig.getConfig().initData(), timeoutPromise]);
        // If we get here, the operation succeeded
        success = true;
        this.logger.info(`系统初始化成功： 第 ${attempt} 次尝试`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'init-config-success',
          millis: Date.now() - startTime,
          extra6: 'success'
        });
      } catch (error) {
        this.logger.error(`系统初始化失败： ${attempt}/${maxRetries} failed: ${error}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'init-config-failed',
          millis: Date.now() - startTime,
          extra6: JSON.stringify({
            error: error
          })
        });
      }
    }
    await this.defaultInit();
    if (!success) {
      this.logger.error(`系统初始化失败： ${maxRetries} 次尝试失败`);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'init-failed-finally',
        millis: Date.now() - startTime
      });
      throw new Error(`系统初始化失败： ${maxRetries} 次尝试失败`);
    }
    setInterval(() => {
      try {
        this.logger.logCPUInfo(AGENT_NAMESPACE, 'INTERVAL_CPU_INFO');
      } catch (e) {}
    }, 10 * 1000);
  }
  async defaultInit() {
    const startTime = Date.now();

    try {
      const config = GlobalConfig.getConfig();
      const dirPath = config.getRepoPath() || config.getCwd();
      // 执行迁移动作
      await migrate(dirPath);

      this.logger.info('init global config successed');
      // 初始化 codeinfo 和 graph

      // init indexManager
      this.cloudIndexManager.dirPath = dirPath;

      await SqliteDb.initTables();
      this.logger.info(`after --- SqliteDb.initTables(); ${dirPath}`);

      // 预加载 trace 配置  放到 getConfig().initData() 后面保证这里可以获取到 baseUrl 的信息
      initTraceConfig().catch((error) => {
        this.logger.warn('Failed to preload trace config:', error);
      });
    } catch (e) {
      this.logger.error(`init codeinfo and graph error, ${e}`);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'init-codeinfo-and-graph-failed',
        millis: Date.now() - startTime,
        extra6: JSON.stringify({
          error: e
        })
      });
    }
    this.messenger.setIsInited(true);
    reportUserRules();
    this.logger.info('setIsInited successed, start listening');
  }
  private async initMcpAndUIPreview() {
    McpClient.getInstance().setMessenger(this.messenger);
    UIPreviewManager.getInstance().setMessenger(this.messenger);
    DeployServerManager.getInstance().setMessenger(this.messenger);
  }
  private registerListeners() {
    const on = this.messenger.on.bind(this.messenger);
    on('state/agentState', async (msg) => {
      const startTime = Date.now();
      try {
        // this.logger.info(`state/agentState, ${JSON.stringify(msg)}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'state/agentState',
          millis: Date.now() - startTime,
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        return {
          status: STATUS.OK
        };
      } catch (e) {
        this.logger.error(`state/agentState error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'state/agentState',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: state/agentState'
        };
      }
    });
    on('index/build', async (msg) => {
      const startTime = Date.now();

      try {
        const dirPath =
          (msg.common as IdeCommonMessage)?.repo?.dir_path ||
          (msg.common as IdeCommonMessage)?.cwd ||
          GlobalConfig.getConfig().getCwd();
        this.cloudIndexManager.dirPath = dirPath;
        this.logger.info(`index/build, ${JSON.stringify(msg)}`);
        this.logger.collectPV('index/build', {
          repo_path: (msg.common as IdeCommonMessage)?.repo?.dir_path
        });
        const result = await this.cloudIndexManager.startExecute();
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/build',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        return {
          status: STATUS.OK,
          data: result
        };
      } catch (e) {
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/build',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        this.logger.error(`index/build error, ${e}`);
        return {
          status: STATUS.FAILED,
          message: 'error: index/build'
        };
      }
    });
    on('index/pause', async (msg) => {
      const startTime = Date.now();
      try {
        this.logger.info(`index/pause, ${JSON.stringify(msg)}`);
        this.logger.collectPV('index/pause', {
          repo_path: (msg.common as IdeCommonMessage)?.repo?.dir_path
        });
        const result = await this.cloudIndexManager.pauseExecute();
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/pause',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        return {
          status: STATUS.OK,
          data: result
        };
      } catch (e) {
        this.logger.error(`index/pause error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/pause',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: index/pause'
        };
      }
    });

    on('index/repoIndex', async (msg) => {
      const startTime = Date.now();
      try {
        this.messenger.request('state/ideInfo', undefined).then(({ data: ideInfo }) => {
          this.logger.reportUserAction({
            key: 'index/repoIndex',
            content: (msg.common as IdeCommonMessage)?.repo?.dir_path,
            type: (msg.common as IdeCommonMessage)?.repo?.git_url,
            subType: ideInfo?.repoInfo?.branch
          });
        });
        this.logger.logProcess('performance-usage-for-index-repo', 'start');
        this.logger.info(`index/repoIndex, ${JSON.stringify(msg)}`);
        this.cloudIndexManager.dirPath = (msg.common as IdeCommonMessage)?.repo?.dir_path;
        const dirPath =
          (msg.common as IdeCommonMessage)?.repo?.dir_path ||
          (msg.common as IdeCommonMessage)?.cwd ||
          GlobalConfig.getConfig().getCwd();
        const commitId = (msg.common as IdeCommonMessage)?.repo?.commit;
        const gitUrl = (msg.common as IdeCommonMessage)?.repo?.git_url || dirPath;
        const ideVersion = (msg.common as IdeCommonMessage)?.version;
        const pluginVersion = (msg.common as IdeCommonMessage)?.pluginVersion;
        const platform = (msg.common as IdeCommonMessage)?.platform;
        if (!dirPath) {
          return {
            status: STATUS.FAILED,
            message: 'error: dir_path is required'
          };
        }
        // 初始化 动态的 tables

        // 是否为用户手动触发
        const manual = !!msg.data?.manual;

        this.cloudIndexManager.processRepo({
          dirPath,
          commitId,
          gitUrl,
          ideVersion,
          pluginVersion,
          platform,
          manual
        });

        this.logger.logProcess('performance-usage-for-index-repo', 'end');
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/repoIndex',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        return {
          status: STATUS.OK
        };
      } catch (e) {
        this.logger.error(`index/repoIndex error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/repoIndex',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: index/repoIndex'
        };
      }
    });

    on('index/file', async (msg) => {
      const startTime = Date.now();

      try {
        this.cloudIndexManager.dirPath = (msg.common as IdeCommonMessage)?.repo?.dir_path;
        const { file, action } = msg.data;
        const repo = (msg.common as IdeCommonMessage)?.repo;
        if (!file || !action || !repo) {
          return {
            status: STATUS.FAILED,
            message: 'error: files or repo_dir is required'
          };
        }
        const { git_url, dir_path } = repo;
        const ignoreFiles = getKwaipilotIgnoreFiles(dir_path);
        const ig = ignore().add(ignoreFiles);
        if (ig.ignores(file)) {
          this.logger.perf({
            namespace: AGENT_NAMESPACE,
            subtag: 'index/file',
            millis: Date.now() - startTime,
            extra6: 'skipped'
          });
        } else {
          this.cloudIndexManager.processFile(file, dir_path, action, git_url);

          this.logger.perf({
            namespace: AGENT_NAMESPACE,
            subtag: 'index/file',
            millis: Date.now() - startTime,
            extra3: GlobalConfig.getConfig().getPlatform(),
            extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
            extra6: 'success'
          });
        }

        return {
          status: STATUS.OK,
          message: 'success'
        };
      } catch (e) {
        this.logger.error(`index/file error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/file',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: index/file'
        };
      }
    });
    on('index/clearIndex', async (msg) => {
      const startTime = Date.now();

      try {
        const dirPath =
          (msg.common as IdeCommonMessage)?.repo?.dir_path ||
          (msg.common as IdeCommonMessage)?.cwd ||
          GlobalConfig.getConfig().getCwd();
        if (!dirPath) {
          return {
            status: STATUS.FAILED,
            message: 'error: dir_path is required'
          };
        }
        this.cloudIndexManager.pauseExecute();
        const result = await this.cloudIndexManager.clearIndex(dirPath);

        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/clearIndex',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        return {
          status: STATUS.OK,
          data: result
        };
      } catch (e) {
        this.logger.error(`index/clearIndex error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/clearIndex',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: index/clearIndex'
        };
      }
    });
    on('index/indexedFileList', async (msg) => {
      const startTime = Date.now();
      try {
        this.cloudIndexManager.initEmbeddingList();
        return {
          status: STATUS.OK,
          message: 'success'
        };
      } catch (e) {
        this.logger.error(`index/indexedFileList error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'index/indexedFileList',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: index/indexedFileList'
        };
      }
    });
    on('state/checkRepoState', async (msg) => {
      const startTime = Date.now();
      try {
        this.logger.info(`state/checkRepoState, ${JSON.stringify(msg)}`);
        this.cloudIndexManager.dirPath = (msg.common as IdeCommonMessage)?.repo?.dir_path;

        const result = await this.cloudIndexManager.getRepoBuildStatus();
        this.logger.info(`state/checkRepoState result, ${JSON.stringify(result)}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'state/checkRepoState',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'success'
        });
        if (!result) {
          return {
            status: STATUS.FAILED,
            message: 'error: repo is not built'
          };
        }
        return {
          status: STATUS.OK,
          data: result
        };
      } catch (e) {
        this.logger.error(`state/checkRepoState error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'state/checkRepoState',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: state/checkRepoState'
        };
      }
    });
    on('search/search', async (msg) => {
      const startTime = Date.now();
      try {
        this.logger.info(`search/search, ${JSON.stringify(msg)}`);
        const { query, chatHistory, topK = 20, targetDirectory = [] } = msg.data;
        if (!query || !chatHistory) {
          return {
            status: STATUS.FAILED,
            message: 'error: query or chatHistory is required'
          };
        }
        const repo = (msg.common as IdeCommonMessage)?.repo;
        const { dir_path, git_url: gitRepo } = repo;
        const dirPath =
          (msg.common as IdeCommonMessage)?.repo?.dir_path ||
          (msg.common as IdeCommonMessage)?.cwd ||
          GlobalConfig.getConfig().getCwd();
        const searchManager = new SearchManager();

        const rerankResult = await searchManager.search({
          query,
          topK,
          targetDirectory,
          chatHistory,
          enable_rewrite: true,
          gitRepo: gitRepo || dir_path || dirPath,
          commit: '',
          username: GlobalConfig.getConfig().getUsername(),
          enableCloudSearch: true,
          dirPath: dir_path || dirPath
        });
        this.logger.info(`search/search result, ${JSON.stringify(rerankResult)}`);

        return {
          status: STATUS.OK,
          data: rerankResult
        };
      } catch (e) {
        this.logger.error(`search/search error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'search/search',
          millis: Date.now() - startTime,
          extra3: GlobalConfig.getConfig().getPlatform(),
          extra4: (msg.common as IdeCommonMessage)?.repo?.dir_path,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: search/search'
        };
      }
    });

    // mcp相关
    on('mcp/getSettingsPath', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.getSettingsPath();
    });
    on('mcp/getAllMcpServers', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.getDisplayServers();
    });
    on('mcp/toggleMcpServer', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.toggleMcpServer(msg.data);
    });
    on('mcp/restartMcpServer', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.restartMcpServer(msg.data);
    });
    on('mcp/deleteMcpServer', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.deleteMcpServer(msg.data);
    });
    on('mcp/installMcp', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.installMcp(msg.data);
    });
    on('mcp/fetchAvailableMcpListByMarket', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.fetchAvailableMcpListByMarket(msg.data);
    });
    on('mcp/fetchMcpDetailByMarket', async (msg) => {
      const mcpClient = McpClient.getInstance();
      return await mcpClient.fetchMcpDetailByMarket(msg.data);
    });

    // agent助理模式
    on('assistant/agent/local', async (msg) => {
      await this.agentMessageHandler.handleAgentMessage(msg);
      return {
        status: STATUS.OK,
        message: 'success'
      };
    });
    on('assistant/agent/getDiffSet', async (msg) => {
      const diffSet = await getDiffSet(msg);
      return {
        status: STATUS.OK,
        data: diffSet
      };
    });
    on('rules/getRulesList', async (msg) => {
      const startTime = Date.now();
      try {
        const rulesList = await getRulesList(msg?.data?.rules || []);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'rules/getRulesList',
          millis: Date.now() - startTime,
          extra6: 'success'
        });
        return {
          status: STATUS.OK,
          data: rulesList
        };
      } catch (e) {
        this.logger.error(`rules/getRulesList error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'rules/getRulesList',
          millis: Date.now() - startTime,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: rules/getRulesList'
        };
      }
    });

    on('state/userLogin', async (msg) => {
      const startTime = Date.now();
      try {
        const username = msg.data?.username;
        GlobalConfig.getConfig().setUsername(username);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'state/userLogin',
          millis: Date.now() - startTime,
          extra6: 'success'
        });
        return {
          status: STATUS.OK
        };
      } catch (e) {
        this.logger.error(`state/userLogin error, ${e}`);
        this.logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'rstate/userLogin',
          millis: Date.now() - startTime,
          extra6: 'failed'
        });
        return {
          status: STATUS.FAILED,
          message: 'error: rules/getRulesList'
        };
      }
    });

    on('state/jwtToken', async (msg) => {
      GlobalConfig.getConfig().setJwtToken(msg.data?.jwtToken);
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'state/jwtToken'
      });
      return {
        status: STATUS.OK
      };
    });

    // ui 预览接口
    on('uiPreview/previewBrowser', async (msg) => {
      const uiPreviewManager = UIPreviewManager.getInstance();
      await uiPreviewManager.previewBrowser(msg.data.url);
      return {
        status: STATUS.OK,
        data: {
          url: msg.data.url
        }
      };
    });
    on('uiPreview/previewProxy', async (msg) => {
      const uiPreviewManager = UIPreviewManager.getInstance();
      const proxyUrl = await uiPreviewManager.previewProxy(msg.data.url);
      return {
        status: STATUS.OK,
        data: {
          url: msg.data.url,
          proxyUrl
        }
      };
    });
    on('uiPreview/installBrowser', async (msg) => {
      const uiPreviewManager = UIPreviewManager.getInstance();

      await uiPreviewManager.installBrowser();
      return {
        status: STATUS.OK
      };
    });
    on('uiPreview/checkPortActive', async (msg) => {
      const uiPreviewManager = UIPreviewManager.getInstance();

      const isActive = await uiPreviewManager.checkPortActive(msg.data.url);
      return {
        status: STATUS.OK,
        data: isActive
      };
    });

    // 部署服务接口
    on('deploy/startServer', async (msg) => {
      const deployServerManager = DeployServerManager.getInstance();

      const response = await deployServerManager.createServer();
      return {
        status: STATUS.OK,
        data: {
          proxyUrl: response.url,
          port: response.port
        }
      };
    });
    on('deploy/stopServer', async (msg) => {
      const deployServerManager = DeployServerManager.getInstance();
      await deployServerManager.stopServer(msg.data.port);
      return {
        status: STATUS.OK
      };
    });

    on('wiki/getWikiList', async (msg) => {
      const wiki = await getWikiList();
      return {
        status: STATUS.OK,
        data: wiki
      };
    });
    on('wiki/checkWikiStatus', async (msg) => {
      const result = await checkProjectWiki();
      return {
        status: STATUS.OK,
        data: result
      };
    });
    on('wiki/generateWiki', async (msg) => {
      initProjectWiki(
        GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd(),
        this.messenger,
        true
      );
      return {
        status: STATUS.OK,
        data: true
      };
    });
    on('wiki/deleteWiki', async (msg) => {
      const status = await deleteProjectWiki();
      return {
        status: STATUS.OK,
        data: status
      };
    });
    on('wiki/cancelGenerate', async (msg) => {
      const cancelled = cancelGenerate();
      return {
        status: STATUS.OK,
        data: {
          status: cancelled,
          message: cancelled ? i18n.__('wiki.cancelled') : i18n.__('wiki.cancell_failed')
        }
      };
    });
  }

  async start() {
    this.ideStateManager.startChecking();
  }

  async stop() {
    this.ideStateManager.stopChecking();
  }
}
