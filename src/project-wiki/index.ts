import { callLLM } from '@/http/llm';
import { AssistantMessage, ToolCall, WikiList, WikiBuildState, WikiCheckResult, WikiProgress } from './types';
import { getProjectStructure, getFileContent, searchCodeBase, writeFile } from './functions';
import { getKwaipilotGlobalPath, md5 } from '@/util/paths';
import { GlobalConfig } from '@/util/global';
import fs from 'fs';
import path from 'path';
import { Logger } from '@/util/log';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import i18n from '@/i18n';

const logger = new Logger('project-wiki');

let globalState = false;

// 全局构建状态管理
let buildState: WikiBuildState = {
  isBuilding: false,
  progress: 0,
  message: ''
};

// 取消标志
let isCancelled = false;

// 进度状态管理函数
const updateBuildState = (isBuilding: boolean, progress: number = 0, message: string = '') => {
  buildState = {
    isBuilding,
    progress,
    message,
    startTime: isBuilding ? buildState.startTime || new Date() : undefined
  };
};

// 获取当前构建状态
export const getBuildState = (): WikiBuildState => {
  return { ...buildState };
};

// 取消Wiki生成
export const cancelGenerate = (): boolean => {
  if (buildState.isBuilding) {
    isCancelled = true;
    updateBuildState(false, buildState.progress, i18n.__('wiki.cancelled'));
    logger.info('Wiki generation cancelled by user');
    return true;
  }
  return false;
};
export const initProjectWiki = async (
  dirPath: string,
  messager: IMessenger<ToCoreProtocol, FromCoreProtocol>,
  force: boolean = false
) => {
  const sendProgress = (progress: number, message: string) => {
    if (isCancelled) {
      updateBuildState(false, 0, i18n.__('wiki.cancelled'));
      return;
    }
    updateBuildState(true, progress, message);
    messager.send('wiki/generateProgress', {
      progress,
      message
    });
  };
  try {
    if (buildState.isBuilding) {
      return;
    }
    // 重置取消标志
    isCancelled = false;
    const checkResult = await checkProjectWiki();
    if (checkResult.exists && !force) {
      updateBuildState(false, 1, i18n.__('wiki.success'));
      return;
    }
    updateBuildState(true, 0.01, i18n.__('wiki.progressing'));

    let finalAnalysis = '';
    let progress = 0.01;
    let miniStep = 0.03;
    let count = 1;

    sendProgress(progress, i18n.__('wiki.progressing'));
    const analysisMessages: any[] = [];

    // Step 1: Get initial analysis
    let { data: currentResponse } = await callLLM(analysisMessages, dirPath);
    while (currentResponse.tool_calls) {
      // 检查是否被取消
      if (isCancelled) {
        logger.info('Wiki generation was cancelled, terminating...');
        return;
      }

      count++;
      // 构建助手消息对象
      const assistantMessage: AssistantMessage = {
        role: 'assistant',
        content: currentResponse.content,
        tool_calls: currentResponse.tool_calls
      };
      analysisMessages.push(assistantMessage);
      logger.info('添加助手消息到历史:', JSON.stringify(assistantMessage, null, 2));
      const completeTask = currentResponse.tool_calls.find((toolCall: ToolCall) => toolCall.name === 'completeTask');
      if (completeTask) {
        // 解析 completeTask 的参数
        const taskParams = completeTask.input;
        const { summary, finishReason } = taskParams;

        if (finishReason === 'success') {
          sendProgress(1, i18n.__('wiki.success'));
          updateBuildState(false, 1, i18n.__('wiki.success'));
        } else {
          // 如果不是成功，通过 sendProgress 返回失败原因
          const failureMessage = `${i18n.__('wiki.error')}: ${summary}`;
          sendProgress(0, failureMessage);
          updateBuildState(false, 0, failureMessage);
        }

        logger.info('completeTask called:', { summary, finishReason });
        break;
      }
      const functionResults = await Promise.all(
        currentResponse.tool_calls.map(async (toolCall: ToolCall) => {
          const functionName = toolCall.name;
          const functionArgs = toolCall.input;
          let result;
          logger.info('执行工具调用:', {
            id: toolCall.id,
            name: functionName,
            args: functionArgs
          });

          if (functionName === 'getProjectStructure') {
            const structureResult = getProjectStructure(functionArgs.dirPath);

            // 检查是否返回了错误（只有包含error字段的对象才是真正的错误）
            if (
              structureResult &&
              typeof structureResult === 'object' &&
              !Array.isArray(structureResult) &&
              'error' in structureResult
            ) {
              const errorInfo = structureResult as { error: string; reason: string };

              // 获取项目根目录路径，判断是否需要对错误进行严格处理
              const projectRootPath = dirPath;
              const isProjectRoot = path.resolve(functionArgs.dirPath) === path.resolve(projectRootPath);

              // 只有在项目根目录且是文件数量不足的错误时才中断流程
              if (isProjectRoot && errorInfo.error === 'TOO_FEW_FILES') {
                // 直接发送失败消息并结束生成过程
                const failureMessage = `${i18n.__('wiki.error')}: ${errorInfo.reason}`;
                sendProgress(0, failureMessage);
                updateBuildState(false, 0, failureMessage);
                logger.error('Project structure validation failed:', errorInfo);

                // 抛出特殊错误来中断整个流程
                throw new Error(`PROJECT_VALIDATION_FAILED: ${errorInfo.reason}`);
              } else {
                // 对于非根目录或其他类型的错误，只记录日志但不中断流程
                logger.warn('Project structure issue (non-critical):', errorInfo);
                result = []; // 返回空数组继续处理
              }
            } else {
              result = structureResult;
            }
          } else if (functionName === 'getFileContent') {
            result = getFileContent(functionArgs.filePath);
          } else if (functionName === 'searchCodeBase') {
            result = await searchCodeBase(functionArgs.query, functionArgs.targetDirectory);
          } else if (functionName === 'writeFile') {
            miniStep = 0.1;
            if (progress < 0.6) {
              progress = 0.6;
            }
            result = writeFile(functionArgs.filePath, functionArgs.content);
            progress = Math.min(progress + miniStep, 0.95);
            sendProgress(progress, i18n.__('wiki.progressing'));
          }
          const toolResult = {
            role: 'tool' as const,
            tool_call_id: toolCall.id,
            content: typeof result === 'string' ? result : JSON.stringify(result)
          };

          logger.info('工具调用结果:', {
            tool_call_id: toolResult.tool_call_id,
            tool_name: toolCall.name,
            contentLength: toolResult.content.length
          });

          return toolResult;
        })
      );

      analysisMessages.push(...functionResults);
      logger.info('当前消息历史长度:', analysisMessages.length);
      logger.info(
        '最后两条消息类型:',
        analysisMessages.slice(-2).map((msg) => ({
          role: msg.role,
          hasToolCalls: 'tool_calls' in msg && (msg.tool_calls?.length ?? 0) > 0,
          hasToolCallId: 'tool_call_id' in msg
        }))
      );

      logger.info(
        '发送给LLM的消息历史:',
        JSON.stringify(
          analysisMessages.map((msg) => ({
            role: msg.role,
            content: msg.content ? msg.content.substring(0, 100) + '...' : undefined,
            tool_calls: 'tool_calls' in msg ? msg.tool_calls?.length : undefined,
            tool_call_id: 'tool_call_id' in msg ? msg.tool_call_id : undefined
          })),
          null,
          2
        )
      );

      // 在调用LLM前再次检查取消状态
      if (isCancelled) {
        logger.info('Wiki generation was cancelled before LLM call, terminating...');
        return;
      }

      const { data: nextResponse } = await callLLM(analysisMessages, dirPath);
      currentResponse = nextResponse;
      if (progress > 0.4) {
        miniStep = 0.01;
      }
      progress = progress <= 0.6 ? Math.min(progress + miniStep, 0.6) : progress;
      sendProgress(progress, i18n.__('wiki.progressing'));
    }
    sendProgress(1, i18n.__('wiki.success'));
    updateBuildState(false, 1, i18n.__('wiki.success'));
    return finalAnalysis;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // 如果是项目验证失败，不需要重复发送错误消息
    if (errorMessage.startsWith('PROJECT_VALIDATION_FAILED:')) {
      logger.info('Project validation failed, terminating wiki generation');
    } else {
      // 其他错误正常处理
      logger.error('Error executing generate wiki:', {
        error: error,
        context: 'generateWiki',
        errorMessage: errorMessage,
        timestamp: new Date().toISOString()
      });
      sendProgress(0, i18n.__('wiki.error'));
      updateBuildState(false, 0, i18n.__('wiki.error'));
    }
  }
};

export const checkProjectWiki = async (): Promise<WikiCheckResult> => {
  const requiredFiles = [
    'ARCHITECTURE.md',
    'DEVELOPMENT_GUIDE.md',
    'PROJECT_OVERVIEW.md',
    'TECH_STACK_AND_PREFERENCES.md'
  ];

  // 获取当前构建状态
  const currentBuildState = getBuildState();

  // 辅助函数：生成不存在的返回结果（带构建状态检查）
  const createNotExistsResult = (): WikiCheckResult => {
    if (currentBuildState.isBuilding || currentBuildState.message) {
      return {
        exists: false,
        buildState: currentBuildState
      };
    }
    return { exists: false };
  };

  // 如果正在构建中，或者有构建状态消息，返回构建状态
  if (currentBuildState.isBuilding) {
    return {
      exists: false,
      buildState: currentBuildState
    };
  }

  if (globalState) {
    return { exists: true, buildState: { isBuilding: false, progress: 1, message: i18n.__('wiki.success') } };
  }
  try {
    // 构建projectWiki文件夹路径，使用与writeFile相同的逻辑
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );

    // 检查projectWiki文件夹是否存在
    if (!fs.existsSync(prefixPath)) {
      return createNotExistsResult();
    }

    // 检查每个必需的文件
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return createNotExistsResult();
      }

      // 检查文件内容是否为空
      try {
        const content = fs.readFileSync(filePath, 'utf8').trim();
        if (content.trim().length === 0) {
          return createNotExistsResult();
        }
      } catch (error) {
        // 如果读取文件失败，返回false
        return createNotExistsResult();
      }
    }
    globalState = true;
    // 所有文件都存在且内容不为空
    return { exists: true, buildState: { isBuilding: false, progress: 1, message: i18n.__('wiki.success') } };
  } catch (error) {
    // 如果发生任何错误，返回false
    logger.error('检查项目信息文件时发生错误:', error);
    return createNotExistsResult();
  }
};

export const getWikiList = async (): Promise<WikiList | null> => {
  const checkResult = await checkProjectWiki();
  if (!checkResult.exists) {
    return null;
  }

  try {
    const requiredFiles = [
      'ARCHITECTURE.md',
      'DEVELOPMENT_GUIDE.md',
      'PROJECT_OVERVIEW.md',
      'TECH_STACK_AND_PREFERENCES.md'
    ];
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );

    const wikis = [];

    // 读取每个文件的内容
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        wikis.push({
          content,
          filename: fileName
        });
      } catch (error) {
        logger.error(`读取文件 ${fileName} 时发生错误:`, error);
        wikis.push({
          content: '',
          filename: fileName
        });
      }
    }

    return {
      wikis,
      path: prefixPath,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    logger.error('获取项目信息详情时发生错误:', error);
    return null;
  }
};
export const deleteProjectWiki = async () => {
  try {
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );
    if (fs.existsSync(prefixPath)) {
      fs.rmSync(prefixPath, { recursive: true, force: true });
      globalState = false;
    }
    return true;
  } catch (error) {
    logger.error('删除项目信息时发生错误:', error);
    return false;
  }
};
