import { Api } from '@/http';
import { SearchManager } from '@/indexing/SearchManager';
import path from 'path';
import { FileNode } from './types';
import fs from 'fs';
import { GlobalConfig } from '@/util/global';
import { getKwaipilotGlobalPath, md5 } from '@/util/paths';
import { Logger } from '@/util/log';
import i18n from '@/i18n';
const logger = new Logger('project-wiki-functions');

/**
 * Comprehensive list of directories and files to ignore during project structure analysis.
 * Based on .gitignore, .eslintignore, .prettierignore and common ignore patterns in the codebase.
 */
const IGNORE_PATTERNS = {
  // Build and development artifacts
  directories: [
    'node_modules',
    'oh_modules',
    '__pycache__',
    '.git',
    '.gradle',
    '.idea',
    '.parcel-cache',
    '.pytest_cache',
    '.next',
    '.nuxt',
    '.sass-cache',
    '.vs',
    '.vscode',
    'Pods',
    'bin',
    'build',
    'bundle',
    'coverage',
    'deps',
    'dist',
    'env',
    'obj',
    'out',
    'pkg',
    'pycache',
    'target',
    'temp',
    'tmp',
    'vendor',
    'venv',
    // Test and mock directories
    'test-repo',
    'mock',
    // Cache directories
    '.cache',
    '.eslintcache',
    '.kwaipilot',
    '.continue'
  ],
  // File patterns to ignore
  filePatterns: [
    // Lock files
    /\.lock$/,
    /package-lock\.json$/,
    /yarn\.lock$/,
    // Log files
    /\.log$/,
    // System files
    /\.DS_Store$/,
    // Build artifacts
    /\.min\.(js|css)$/,
    /\.map$/,
    // Backup files
    /\.(bak|old|tmp)$/,
    // Binary and media files (common ones)
    /\.(jpg|jpeg|png|gif|ico|svg|mp3|mp4|avi|zip|tar|gz|exe|dll|so|dylib)$/i
  ]
};

/**
 * Check if an item (file or directory) should be ignored
 */
function shouldIgnoreItem(itemName: string, isDirectory: boolean = false): boolean {
  // Check if it's a directory in the ignore list
  if (IGNORE_PATTERNS.directories.includes(itemName)) {
    return true;
  }

  // 如果是以点开头的项目
  if (itemName.startsWith('.')) {
    // 对于目录：过滤所有以点开头的目录（如 .git, .vscode 等）
    if (isDirectory) {
      return true;
    }
    // 对于文件：不过滤，保留所有以点开头的文件（包括配置文件）
    return false;
  }

  // Check against file patterns
  return IGNORE_PATTERNS.filePatterns.some((pattern) => pattern.test(itemName));
}

export function getFileContent(filePath: string) {
  try {
    // Check if path exists and is a file
    const stats = fs.statSync(filePath);
    if (!stats.isFile()) {
      return `${i18n.__('wiki.error.notAFile')}: ${filePath}`;
    }
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    if (error instanceof Error) {
      return `${i18n.__('wiki.error.readFileFailed')} ${filePath}: ${error.message}`;
    }
    return `${i18n.__('wiki.error.readFileFailed')} ${filePath}: Unknown error`;
  }
}

export function getProjectStructure(
  dirPath: string,
  currentDepth: number = 1
): FileNode[] | { error: string; reason: string } {
  const MAX_DEPTH = 2;
  const MIN_FILES_THRESHOLD = 5; // 最少文件数量阈值

  // 获取项目根目录路径，用于判断是否需要检查文件数量
  const projectRootPath = GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd();
  const shouldCheckFileCount = path.resolve(dirPath) === path.resolve(projectRootPath);

  logger.info(`Checking directory: ${dirPath}`);
  logger.info(`Project root path: ${projectRootPath}`);
  logger.info(`Should check file count: ${shouldCheckFileCount}`);

  try {
    // Check if directory exists and is accessible
    if (!fs.existsSync(dirPath)) {
      logger.warn(`Directory does not exist: ${dirPath}`);
      return { error: 'EMPTY_DIRECTORY', reason: `${i18n.__('wiki.error.emptyDirectory')}: ${dirPath}` };
    }

    const stats = fs.statSync(dirPath);
    if (!stats.isDirectory()) {
      logger.info(`Path is not a directory, skipping: ${dirPath}`);
      return []; // 返回空数组而不是错误
    }

    const items = fs.readdirSync(dirPath);
    const result: FileNode[] = [];

    // 如果目录完全为空
    if (items.length === 0) {
      return { error: 'EMPTY_DIRECTORY', reason: i18n.__('wiki.error.emptyDirectory') };
    }

    for (const item of items) {
      try {
        const fullPath = path.join(dirPath, item);
        const stats = fs.statSync(fullPath);

        // Skip directories and files that should be ignored based on project standards
        if (shouldIgnoreItem(item, stats.isDirectory())) {
          continue;
        }

        if (stats.isDirectory()) {
          let children: FileNode[] = [];

          if (currentDepth < MAX_DEPTH) {
            const childResult = getProjectStructure(fullPath, currentDepth + 1);
            // 如果子目录返回错误，我们仍然继续处理，但不添加子项
            if (Array.isArray(childResult)) {
              children = childResult;
            } else {
              // 记录子目录错误但继续处理
              logger.warn(`Skipping subdirectory ${fullPath} due to error:`, childResult.reason);
            }
          }

          const dirNode: FileNode = {
            name: item,
            type: 'directory',
            depth: currentDepth,
            children
          };
          result.push(dirNode);
        } else {
          result.push({
            name: item,
            type: 'file',
            depth: currentDepth
          });
        }
      } catch (itemError) {
        // Log individual item errors but continue processing other items
        logger.warn(
          `Error processing item ${item} in ${dirPath}:`,
          itemError instanceof Error ? itemError.message : 'Unknown error'
        );
        continue;
      }
    }

    // 检查有效文件和目录的数量
    const totalItems = result.length;
    const fileCount = result.filter((item) => item.type === 'file').length;

    // 如果过滤后没有任何有效的文件或目录
    if (totalItems === 0) {
      return {
        error: 'EMPTY_DIRECTORY',
        reason: i18n.__('wiki.error.emptyDirectory')
      };
    }

    // 只有当dirPath是项目根目录时才检查文件数量
    if (shouldCheckFileCount && fileCount < MIN_FILES_THRESHOLD && totalItems < MIN_FILES_THRESHOLD) {
      return {
        error: 'TOO_FEW_FILES',
        reason: `${i18n.__('wiki.error.tooFewFiles')} (${fileCount} files, ${totalItems - fileCount} directories)`
      };
    }

    return result;
  } catch (error) {
    logger.error(`Error reading directory ${dirPath}:`, error instanceof Error ? error.message : 'Unknown error');
    return {
      error: 'READ_ERROR',
      reason: `${i18n.__('wiki.error.readError')}: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

export async function searchCodeBase(query: string, targetDirectory: string[] = []) {
  const searchManager = new SearchManager();
  const searchResult = await searchManager.search({
    query,
    topK: 20,
    targetDirectory: targetDirectory || [],
    chatHistory: [],
    enable_rewrite: false,
    username: GlobalConfig.getConfig().getUsername(),
    gitRepo: GlobalConfig.getConfig().getRepoPath(),
    dirPath: GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd()
  });
  return new Set(
    searchResult.code_context_list.map((item) => {
      return {
        code_content: item.code_content,
        file_path: item.metadata.file_path
      };
    })
  );
}

export function writeFile(filePath: string, content: string): { success: boolean; error?: string; message?: string } {
  try {
    if (!filePath) {
      return {
        success: false,
        error: i18n.__('wiki.error.filePathRequired'),
        message: i18n.__('wiki.error.filePathRequired')
      };
    }
    if (!content) {
      return { success: false, error: i18n.__('wiki.error.contentRequired') };
    }
    logger.info('writeFile', filePath, content);
    const prefixPath = path.join(
      getKwaipilotGlobalPath(),
      'rules',
      'projectWiki',
      md5(GlobalConfig.getConfig().getRepoPath() || GlobalConfig.getConfig().getCwd())
    );
    if (!fs.existsSync(prefixPath)) {
      fs.mkdirSync(prefixPath, { recursive: true });
    }
    fs.writeFileSync(path.join(prefixPath, filePath), content);
    return { success: true, message: `file successfully written to ${path.join(prefixPath, filePath)}` };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return { success: false, error: `${i18n.__('wiki.error.writeFileFailed')}: ${errorMessage}` };
  }
}
