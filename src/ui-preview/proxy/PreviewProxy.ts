import express from 'express';
import { createProxyMiddleware, responseInterceptor } from 'http-proxy-middleware';
import { createServer, Server } from 'http';
import { Socket } from 'net';
import { PreviewProxyStatus, PreviewProxyResponse } from './types';
import { SCRIPT_URL_INTERNAL, SCRIPT_URL_EXTERNAL } from './constants';
import { UIPreviewManager } from '..';
import { LoggerManager } from '../LoggerManager';
import { GlobalConfig } from '@/util/global';

/**
 * 预览代理服务器
 */
export class PreviewProxy {
  private target: string;
  private port: number;
  private server?: Server;
  private status: PreviewProxyStatus = PreviewProxyStatus.STOPPED;
  private app = express();
  private connections = new Set<Socket>();

  private logger = new LoggerManager(`uiPreview-previewProxy`);

  constructor(target: string, port: number) {
    this.target = target;
    this.port = port;
    this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Created proxy for ${this.target}`);
  }

  private getLoopbackHostForTarget(): string {
    try {
      const url = new URL(this.target);
      const rawHostname = url.hostname;
      const normalized = rawHostname.replace(/^\[|\]$/g, '');
      // 显式 IPv6 或未指定 IPv6 的场景，使用 ::1；其余使用 127.0.0.1
      if (normalized === '::1' || normalized === '::') {
        return '::1';
      }
      // 若目标本身是 IPv6 地址（包含冒号），也返回 ::1
      if (normalized.includes(':')) {
        return '::1';
      }
      return '127.0.0.1';
    } catch {
      return '127.0.0.1';
    }
  }

  private formatHostForUrl(host: string): string {
    return host.includes(':') ? `[${host}]` : host;
  }

  public async start(): Promise<void> {
    this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Starting proxy for ${this.target}`);
    this.status = PreviewProxyStatus.STARTING;

    try {
      this.server = createServer(this.app);

      this.server.on('connection', (socket: Socket) => {
        this.connections.add(socket);
        socket.on('close', () => {
          this.connections.delete(socket);
        });
      });

      // 在启动时设置中间件
      this.setupMiddleware();

      const listenHostStart = this.getLoopbackHostForTarget();
      await new Promise<void>((resolve, reject) => {
        this.server?.listen(this.port, listenHostStart, () => {
          resolve();
        });
        this.server?.on('error', reject);
      });

      this.status = PreviewProxyStatus.RUNNING;
      const listenHostRun = this.getLoopbackHostForTarget();
      const displayHost = this.formatHostForUrl(listenHostRun);
      this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Started proxy for ${this.target} at http://${displayHost}:${this.port}`); //-
    } catch (error) {
      this.status = PreviewProxyStatus.ERROR;
      this.logger.getUIPreviewLogger().error(`[PreviewProxy:${this.port}] Failed to start proxy for ${this.target}`, error);
      throw new Error(`[PreviewProxy:${this.port}] Failed to start proxy: ${error instanceof Error ? error.message : 'Unknown error'}`); // -
    }
  }

  public async stop(): Promise<void> {
    this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Stopping proxy for ${this.target}`);

    try {
      if (this.server) {
        // 强制关闭所有活跃连接
        this.connections.forEach(socket => {
          if (!socket.destroyed) {
            socket.destroy();
          }
        });
        this.connections.clear();

        // 停止服务器并等待完全关闭
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Server close timeout'));
          }, 5000); // 5秒超时

          this.server?.close((error) => {
            clearTimeout(timeout);
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });

        // 清理服务器引用
        this.server = undefined;
      }

      this.status = PreviewProxyStatus.STOPPED;
      this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Stopped proxy for ${this.target}`);
    } catch (error) {
      this.status = PreviewProxyStatus.ERROR;
      this.logger.getUIPreviewLogger().error(`[PreviewProxy:${this.port}] Error stopping proxy for ${this.target}:`, error); // -
      throw new Error(`[PreviewProxy:${this.port}] Failed to stop proxy: ${error instanceof Error ? error.message : 'Unknown error'}`); // -
    }
  }

  private setupMiddleware(): void {
    // 处理工具栏消息
    this.app.post('/__ui_bridge__/send', express.json(), (req, res) => {
      this.handleMessage(req, res);
    });

    // 处理埋点上报请求
    this.app.post('/__ui_bridge__/log', express.json(), (req, res) => {
      this.handleLogReport(req, res);
    });

    // 创建代理中间件实例
    const proxyMiddleware = createProxyMiddleware({
      target: this.getTargetBaseUrl(),
      changeOrigin: true,
      selfHandleResponse: true,
      ws: true,
      on: {
        proxyRes: responseInterceptor(async (responseBuffer, proxyRes, req, res) => {
          // 处理响应头
          // this.handleResponseHeaders(proxyRes, req, res);
          // 处理工具栏脚本注入
          return this.injectToolbarScript(responseBuffer, proxyRes, req, res);
        }),
        error: (err: any, req: any, res: any) => {
          this.logger.getUIPreviewLogger().error(`[PreviewProxy:${this.port}] Error on ProxyMiddleware for ${this.target}:`, err); // -
        },
      }
    });

    this.app.use('/', proxyMiddleware);

    // 设置WebSocket升级处理
    this.server?.on('upgrade', (req, socket, head) => {
      proxyMiddleware.upgrade(req, socket as Socket, head);
    });
  }

  private handleResponseHeaders(proxyRes: any, req: any, res: any): void {
    const headersToPass = [
      'content-type',
      'content-encoding',
      'cache-control',
      'etag',
      'last-modified',
      'expires',
      'vary'
    ];

    headersToPass.forEach(headerName => {
      if (proxyRes.headers[headerName]) {
        res.setHeader(headerName, proxyRes.headers[headerName]);
      }
    });
  }

  private async injectToolbarScript(
    responseBuffer: Buffer,
    proxyRes: any,
    req: any,
    res: any
  ): Promise<Buffer> {
    const contentType = proxyRes.headers['content-type'] || '';

    if (!contentType.includes('text/html')) {
      return responseBuffer;
    }
    const html = responseBuffer.toString('utf8');
    // 监听iframe的url变化，并通知父窗口
    const injectScript = `
        <script>
          (function () {
            function notifyParent() {
              try {
                if (window.parent !== window) {
                  window.parent.postMessage({ type: 'iframeUrlChanged', url: location.href }, '*');
                }
              } catch (e) {}
            }
            window.addEventListener('popstate', notifyParent);

            function wrapHistoryMethod(type) {
              const original = history[type];
              history[type] = function () {
                const res = original.apply(this, arguments);
                notifyParent();
                return res;
              };
            }
            wrapHistoryMethod('pushState');
            wrapHistoryMethod('replaceState');

            // notifyParent();
          })();
          window.addEventListener('message', (event) => {
            // Only handle messages from the parent window
            if (event.source === window.parent && event.data) {
              switch (event.data.type) {
                case 'goBack':
                  history.back();
                  break;
                case 'goForward':
                  history.forward();
                  break;
                case 'navigateTo':
                  const url = event.data.url;
                  const requestId = event.data.requestId;
                  console.log('[iframe] recv navigateTo', { url, requestId });
                  if (!url) break;
                  try {
                    const target = new URL(url, location.href);
                    if (target.origin === location.origin) {
                      history.pushState({}, '', target.href);
                      window.dispatchEvent(new PopStateEvent('popstate'));
                      if (window.parent !== window) {
                        window.parent.postMessage({ type: 'navigate-ack', url: target.href, requestId }, '*');
                      }
                    } else {
                      location.href = target.href;
                    }
                  } catch (e) {}
                  break;
                default:
                  break;
              }
            }
          });
        </script>
    `;
    const iframeInjectScript = `<script>window['__IDE_INJECTOR__'] = true;</script>`;
    const script_url = GlobalConfig.getConfig().getVersionType() !== 'External' ? SCRIPT_URL_INTERNAL : SCRIPT_URL_EXTERNAL;
    // 监听插件传递出来的消息
    const injectMessageScript = `
        <script>
          window.addEventListener('message', (event) => {
              if (event.data && event.data.type === 'consoleError') {
                  console.log('收到来自js插件的消息', event.data);
                   try {
                      if (window.parent !== window) {
                        window.parent.postMessage({type: 'reportConsoleError', data: event.data.data}, '*');
                      }
                    } catch (e) {}
                
              }
          });
        </script>
    `
    return Buffer.from(html.replace('</head>', `${injectMessageScript}${iframeInjectScript}<script src="${script_url}?t=${Date.now()}"></script>${injectScript}</head>`));
  }

  private handleMessage(req: express.Request, res: express.Response): void {
    try {
      UIPreviewManager.getInstance().sendInfo(req.body.data);
      res.json({
        success: true,
        message: 'success',
        data: req.body.data,
        timestamp: Date.now()
      } as PreviewProxyResponse);
    } catch (error) {
      this.logger.getUIPreviewLogger().error(`[PreviewProxy:${this.port}] Failed to handle message for ${this.target}:`, error); // -
      res.status(500).json({
        success: false,
        message: 'failed',
        timestamp: Date.now()
      } as PreviewProxyResponse);
    }
  }

  // 处理埋点上报请求
  private handleLogReport(req: express.Request, res: express.Response): void {
    try {
      UIPreviewManager.getInstance().reportUserAction(req.body);
      res.json({
        success: true,
        message: 'success',
        timestamp: Date.now()
      } as PreviewProxyResponse);
    } catch (error) {
      this.logger.getUIPreviewLogger().error(`[PreviewProxy:${this.port}] Failed to handle log request for ${this.target}:`, error); // -
      res.status(500).json({
        success: false,
        message: 'failed',
        timestamp: Date.now()
      } as PreviewProxyResponse);
    }
  }

  /**
   * 获取目标的基础URL（只包含域名和端口）
   */
  private getTargetBaseUrl(): string {
    try {
      const url = new URL(this.target);
      return `${url.protocol}//${url.host}`;
    } catch (error) {
      this.logger.getUIPreviewLogger().warn(`[PreviewProxy:${this.port}] Failed to parse target URL: ${this.target}`, error); // -
      return this.target;
    }
  }

  /**
   * 获取代理URL
   */
  public getUrl(): string {
    try {
      const url = new URL(this.target);
      const host = this.getLoopbackHostForTarget();
      const baseHost = this.formatHostForUrl(host);
      const proxyUrl = new URL(`http://${baseHost}:${this.port}`);

      // 复制原始URL的路径和查询参数
      proxyUrl.pathname = url.pathname;
      proxyUrl.search = url.search;
      proxyUrl.hash = url.hash;

      return proxyUrl.toString();
    } catch (error) {
      this.logger.getUIPreviewLogger().warn(`[PreviewProxy:${this.port}] Failed to build proxy URL for ${this.target}:`, error); // -
      const host = this.getLoopbackHostForTarget();
      const displayHost = this.formatHostForUrl(host);
      return `http://${displayHost}:${this.port}`;
    }
  }


  public getStatus(): PreviewProxyStatus {
    return this.status;
  }

  public getTarget(): string {
    return this.target;
  }

  public getPort(): number {
    return this.port;
  }

  public async healthCheck(): Promise<boolean> {
    // 首先检查代理服务器是否运行
    if (this.status !== PreviewProxyStatus.RUNNING) {
      return false;
    }

    // 检查目标服务器是否可达
    try {
      const response = await fetch(this.target);
      return response.status < 500;
    } catch (error) {
      this.logger.getUIPreviewLogger().warn(`[PreviewProxy:${this.port}] Health check failed for target ${this.target}:`, error); // -
      return false;
    }
  }

  public refresh(): void {
    UIPreviewManager.getInstance().notifyRefresh(this.target);
    this.logger.getUIPreviewLogger().info(`[PreviewProxy:${this.port}] Refresh requested for ${this.target}`);
  }
}