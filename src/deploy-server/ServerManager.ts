import { DeployServer } from './DeployServer';
import { HttpServerInfo, DeployServerStatus } from './types';
import getPort, { portNumbers } from 'get-port';
import { LoggerManager } from './LoggerManager';

/**
 * 内部服务数据结构
 */
interface ServerData {
  instance: DeployServer;
  createdAt: Date;
  lastActiveAt: Date;
}

/**
 * 简化的HTTP服务管理器 - 单例模式
 */
export class ServerManager {
  private static instance: ServerManager | null = null;

  private servers: Map<number, ServerData> = new Map();
  private readonly PORT_RANGE_START = 3000;
  private readonly PORT_RANGE_END = 4000;

  private logger = new LoggerManager(`httpServer-manager`);

  private constructor() {
    this.logger.getLogger().info('[ServerManager] Instance created');
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ServerManager {
    if (!ServerManager.instance) {
      ServerManager.instance = new ServerManager();
    }
    return ServerManager.instance;
  }

  /**
   * 查找可用端口
   */
  private async findAvailablePort(): Promise<number> {
    try {
      const port = await getPort({
        port: portNumbers(this.PORT_RANGE_START, this.PORT_RANGE_END)
      });
      return port;
    } catch (error) {
      throw new Error(`Failed to find available port: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 创建HTTP服务
   */
  public async createServer(port?: number): Promise<HttpServerInfo> {
    try {
      // 如果没有指定端口或端口已被占用，则自动分配
      const assignedPort = port && !this.servers.has(port) ? port : await this.findAvailablePort();
      
      // 检查是否已存在相同端口的服务
      if (this.servers.has(assignedPort)) {
        const existingServer = this.servers.get(assignedPort)!;
        this.logger.getLogger().info(`[ServerManager] Server on port ${assignedPort} already exists`);
        
        // 更新活跃时间
        existingServer.lastActiveAt = new Date();
        return existingServer.instance.getInfo();
      }

      this.logger.getLogger().info(`[ServerManager] Creating server on port ${assignedPort}`);

      // 创建服务实例
      const serverInstance = new DeployServer(assignedPort);

      // 启动服务
      await serverInstance.start();

      // 注册到管理器
      const now = new Date();
      this.servers.set(assignedPort, {
        instance: serverInstance,
        createdAt: now,
        lastActiveAt: now
      });

      this.logger.getLogger().info(`[ServerManager] Server created successfully on port ${assignedPort}`);

      return {
        id: serverInstance.getId(),
        port: assignedPort,
        url: serverInstance.getUrl(),
        status: serverInstance.getStatus(),
        createdAt: now,
        lastActiveAt: now,
        isHealthy: true
      };

    } catch (error) {
      throw new Error(`Failed to create server: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 停止服务
   */
  public async stopServer(port: number): Promise<void> {
    const serverData = this.servers.get(port);
    if (!serverData) {
      throw new Error(`Server on port ${port} not found`);
    }

    try {
      await serverData.instance.stop();
      this.logger.getLogger().info(`[ServerManager] Server on port ${port} stopped successfully`);
      this.servers.delete(port);
    } catch (error) {
      throw new Error(`Failed to stop server on port ${port}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取服务状态
   */
  public async getServerInfo(port: number): Promise<HttpServerInfo | null> {
    const serverData = this.servers.get(port);
    if (!serverData) {
      return null;
    }

    const { instance, createdAt, lastActiveAt } = serverData;
    const isHealthy = await instance.healthCheck();

    return {
      id: instance.getId(),
      port,
      url: instance.getUrl(),
      status: instance.getStatus(),
      createdAt,
      lastActiveAt,
      isHealthy
    };
  }

  /**
   * 获取所有服务
   */
  public async getAllServers(): Promise<HttpServerInfo[]> {
    const serverEntries = Array.from(this.servers.entries());

    const serverPromises = serverEntries.map(async ([port, serverData]) => {
      const { instance, createdAt, lastActiveAt } = serverData;

      try {
        const isHealthy = await instance.healthCheck();
        return {
          id: instance.getId(),
          port,
          url: instance.getUrl(),
          status: instance.getStatus(),
          createdAt,
          lastActiveAt,
          isHealthy
        };
      } catch (error) {
        this.logger.getLogger().warn(`[ServerManager] Failed to get health status for server on port ${port}:`, error);
        return {
          id: instance.getId(),
          port,
          url: instance.getUrl(),
          status: instance.getStatus(),
          createdAt,
          lastActiveAt,
          isHealthy: false
        };
      }
    });

    return Promise.all(serverPromises);
  }

  /**
   * 获取某一个服务
   */
  public getServer(port?: number): DeployServer | null{
        if (!port) {
            return null;
        }
        const serverData = this.servers.get(port);
        console.log('vercel server data:', {
            hasInstance: !!serverData?.instance,
            createdAt: serverData?.createdAt,
            lastActiveAt: serverData?.lastActiveAt,
            instanceId: serverData?.instance?.getId(),
            instanceStatus: serverData?.instance?.getStatus()
        });
        if (!serverData) {  
            return null;
        }  
        return serverData.instance; 
    }

  /**
   * 停止所有服务
   */
  public async stopAllServers(): Promise<void> {
    this.logger.getLogger().info('[ServerManager] Stopping all servers...');

    const ports = Array.from(this.servers.keys());
    const errors: string[] = [];

    for (const port of ports) {
      try {
        await this.stopServer(port);
      } catch (error) {
        const errorMsg = `Failed to stop server on port ${port}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Some servers failed to stop: ${errors.join('; ')}`);
    }

    this.logger.getLogger().info('[ServerManager] All servers stopped successfully');
  }

  /**
   * 清理资源
   */
  public async dispose(): Promise<void> {
    this.logger.getLogger().info('[ServerManager] Disposing ServerManager...');

    try {
      if (this.servers.size > 0) {
        await this.stopAllServers();
      }

      this.logger.getLogger().info('[ServerManager] ServerManager disposed successfully');
    } catch (error) {
      this.logger.getLogger().error('[ServerManager] Error during dispose:', error);
      throw error;
    }
  }
}