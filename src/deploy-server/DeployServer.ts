import express from 'express';
import cors from 'cors';
import { createServer, Server } from 'http';
import { Socket } from 'net';
import { DeployServerStatus, HttpServerInfo, ApiResponse, VERCEL_CLIENT_ID, VERCEL_CLIENT_SECRET } from './types';
import { LoggerManager } from './LoggerManager';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
/**
 * 简单的HTTP服务实例
 */
export class DeployServer {
  private id: string;
  private port: number;
  private server?: Server;
  private status: DeployServerStatus = DeployServerStatus.STOPPED;
  private app = express();
  private connections = new Set<Socket>();
  private logger = new LoggerManager(`DeployServer`);

  private messenger?: IMessenger<ToCoreProtocol, FromCoreProtocol>;
  


  constructor(port: number) {
    this.id = `http-server-${port}`;
    this.port = port;
    this.logger.getLogger().info(`[DeployServer:${this.id}] Created server instance on port ${this.port}`);
  }
  // 添加设置 messenger 的方法
  public setMessenger(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    console.log(' vercel Setting messenger: messenger instance set');
    this.messenger = messenger;
  }

  /**
   * 启动服务
   */
  public async start(): Promise<void> {
    this.logger.getLogger().info(`[DeployServer:${this.id}] Starting server on port ${this.port}`);
    this.status = DeployServerStatus.STARTING;

    try {
      this.server = createServer(this.app);

      // 跟踪所有连接
      this.server.on('connection', (socket: Socket) => {
        this.connections.add(socket);
        socket.on('close', () => {
          this.connections.delete(socket);
        });
      });

      // 设置中间件和路由
      this.setupMiddleware();
      this.setupRoutes();
      this.setupErrorHandling();

      // 启动服务器 - 监听所有网络接口以支持外部访问
      await new Promise<void>((resolve, reject) => {
        this.server?.listen(this.port, '0.0.0.0', () => {
          resolve();
        });
        this.server?.on('error', reject);
      });

      this.status = DeployServerStatus.RUNNING;
      this.logger.getLogger().info(`[DeployServer:${this.id}] Server started successfully on http://0.0.0.0:${this.port}`);
    } catch (error) {
      this.status = DeployServerStatus.ERROR;
      this.logger.getLogger().error(`[DeployServer:${this.id}] Failed to start server`, error);
      throw new Error(`Failed to start HTTP server: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 停止服务
   */
  public async stop(): Promise<void> {
    this.logger.getLogger().info(`[DeployServer:${this.id}] Stopping server`);

    try {
      if (this.server) {
        // 强制关闭所有活跃连接
        this.connections.forEach(socket => {
          if (!socket.destroyed) {
            socket.destroy();
          }
        });
        this.connections.clear();

        // 停止服务器并等待完全关闭
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Server close timeout'));
          }, 5000);

          this.server?.close((error) => {
            clearTimeout(timeout);
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });

        this.server = undefined;
      }

      this.status = DeployServerStatus.STOPPED;
      this.logger.getLogger().info(`[DeployServer:${this.id}] Server stopped successfully`);
    } catch (error) {
      this.status = DeployServerStatus.ERROR;
      this.logger.getLogger().error(`[DeployServer:${this.id}] Error stopping server`, error);
      throw new Error(`Failed to stop HTTP server: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 设置中间件
   */
  private setupMiddleware(): void {
    // 请求日志中间件
    this.app.use((req, res, next) => {
      this.logger.getLogger().info(`[DeployServer:${this.id}] ${req.method} ${req.url}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });

    // CORS配置 - 支持外部调用
    this.app.use(cors({
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: false,
      optionsSuccessStatus: 200
    }));

    // 解析请求体
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 设置响应头
    this.app.use((req, res, next) => {
      res.setHeader('X-Powered-By', 'Kwaipilot-HTTP-Server');
      res.setHeader('X-Content-Type-Options', 'nosniff');
      next();
    });
  }

  /**
   * 设置路由
   */
  private setupRoutes(): void {
    // 根路径 - 服务信息
    this.app.get('/', (req, res) => {
      res.json({
        success: true,
        message: `Simple HTTP Server ${this.id}`,
        data: {
          id: this.id,
          port: this.port,
          status: this.status,
          uptime: process.uptime()
        },
        timestamp: Date.now()
      } as ApiResponse);
    });

    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'Server is healthy',
        data: {
          id: this.id,
          status: this.status,
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          timestamp: new Date().toISOString()
        },
        timestamp: Date.now()
      } as ApiResponse);
    });

    // OAuth 交换端点
    this.app.post('/api/exchange', async (req, res) => {
      try {
        console.log(`[DeployServer:${this.id}] 收到 OAuth 交换请求:`, {
          method: req.method,
          body: req.body,
          headers: req.headers
        });

        const { code, redirect_uri, code_verifier } = req.body || {};


        
        if (!code || !redirect_uri) {
          this.logger.getLogger().warn(`[DeployServer:${this.id}] 缺少必要参数:`, { 
            code: !!code, 
            redirect_uri: !!redirect_uri 
          });
          return res.status(400).json({
            success: false,
            message: 'missing code/redirect_uri',
            data: { 
              received: { code: !!code, redirect_uri: !!redirect_uri }
            },
            timestamp: Date.now()
          } as ApiResponse);
        }

        // 从环境变量获取配置

        if (!VERCEL_CLIENT_ID || !VERCEL_CLIENT_SECRET) {
          return res.status(500).json({
            success: false,
            message: 'Server configuration error: missing Vercel credentials',
            timestamp: Date.now()
          } as ApiResponse);
        }

        const form = new URLSearchParams({
          client_id: VERCEL_CLIENT_ID,
          client_secret: VERCEL_CLIENT_SECRET,
          code,
          redirect_uri,
          grant_type: 'authorization_code',
        });
        
        if (code_verifier) {
          form.set('code_verifier', code_verifier);
        }

        this.logger.getLogger().info(`[DeployServer:${this.id}] 向 Vercel API 发送请求...`, form.toString());
        

        console.log(`[DeployServer:${this.id}] 向 Vercel API 发送请求...`, form.toString());

        const response = await fetch('https://api.vercel.com/v2/oauth/access_token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: form,
        });

        const responseText = await response.text();
        this.logger.getLogger().info(`[DeployServer:${this.id}] Vercel API 响应:`, {
          status: response.status,
          statusText: response.statusText,
          body: responseText
        });

        console.log(
          `[DeployServer:${this.id}] Vercel API 响应:`, {
          status: response.status,
          statusText: response.statusText,
          body: responseText
        }
        )

        const responseData = JSON.parse(responseText);
        // 把responseData  传递给vscode 插件，使用“deploy/tocken”
        console.log('Vercel responseData 响应:', responseData);
        console.log('Vercel this.messenger available:', !!this.messenger);

        // 检查响应是否成功
        if (responseData.access_token) {
          // 通过 messenger 发送 token 信息
          this.messenger?.send('deploy/tocken', {
            access_token: responseData.access_token,
            user_id: responseData.user_id || '',
            team_id: responseData.team_id || '',
            installation_id: responseData.installation_id || '',
            token_type: responseData.token_type || 'Bearer'
          });

          // 只返回成功状态，不包含敏感信息
          res.status(200).json({
            success: true,
            message: 'OAuth exchange successful',
            timestamp: Date.now()
          });
        } else {
          // 失败情况，返回错误信息（不包含敏感数据）
          res.status(response.status).json({
            success: false,
            message: responseData?.error_description || responseData?.error || 'OAuth exchange failed',
            timestamp: Date.now()
          });
        }
        
      } catch (error: any) {
        this.logger.getLogger().error(`[DeployServer:${this.id}] OAuth 交换错误:`, error);
        res.status(500).json({
          success: false,
          message: String(error?.message || error),
          timestamp: Date.now()
        } as ApiResponse);
      }
    });

    // GET方式的API端点
    this.app.get('/api/info', (req, res) => {
      res.json({
        success: true,
        message: 'Server info',
        data: {
          server: this.id,
          port: this.port,
          status: this.status,
          endpoints: ['/', '/health', '/api/data', '/api/info', '/api/exchange']
        },
        timestamp: Date.now()
      } as ApiResponse);
    });
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 全局错误处理中间件
    this.app.use((err: any, req: any, res: any, next: any) => {
      this.logger.getLogger().error(`[DeployServer:${this.id}] Unhandled server error`, {
        error: err.message,
        url: req.url,
        method: req.method
      });

      res.status(500).json({
        success: false,
        message: 'Internal server error',
        timestamp: Date.now()
      } as ApiResponse);
    });

    // 404处理
    this.app.use((req, res) => {
      this.logger.getLogger().warn(`[DeployServer:${this.id}] 404 Not Found`, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(404).json({
        success: false,
        message: 'Endpoint not found',
        data: {
          availableEndpoints: ['/', '/health', '/api/data', '/api/info', '/api/exchange']
        },
        timestamp: Date.now()
      } as ApiResponse);
    });
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    if (this.status !== DeployServerStatus.RUNNING) {
      return false;
    }

    try {
      const response = await fetch(`http://127.0.0.1:${this.port}/health`, {
        signal: AbortSignal.timeout(5000)
      });
      return response.status === 200;
    } catch (error) {
      this.logger.getLogger().warn(`[DeployServer:${this.id}] Health check failed`, error);
      return false;
    }
  }

  // Getters
  public getId(): string { return this.id; }
  public getPort(): number { return this.port; }
  public getStatus(): DeployServerStatus { return this.status; }
  public getUrl(): string { return `http://127.0.0.1:${this.port}`; }
  public getInfo(): HttpServerInfo {
    return {
      id: this.id,
      port: this.port,
      url: this.getUrl(),
      status: this.status,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      isHealthy: this.status === DeployServerStatus.RUNNING
    };
  }
}