import { type ToolUse, ToolResponse, Ask } from './type.d';

export { ToolUse } from './type.d';

export type AssistantMessageContent = TextContent | ToolUse;

export type PushToolResult = (content: ToolResponse) => void;

export type RemoveClosingTag = (tag: ToolParamName, content?: string) => string;

export type ReportToolAction = (duration: number, params: Record<string, any>) => void;

export type HandleError = (action: string, error: Error, toolName: ToolUseName) => Promise<void>;

export type AskApproval = (type: Ask, partialMessage?: string) => Promise<boolean>;

export interface TextContent {
  type: 'text';
  content: string;
  partial: boolean;
}

export const toolUseNames = [
  'update_phase',
  'research',
  'render_research_plan',
  'execute_command',
  'read_file',
  'edit_file',
  'replace_in_file',
  'write_to_file',
  'codebase_search',
  'grep_search',
  'list_files',
  // "list_code_definition_names",
  'use_mcp_tool',
  'ask_followup_question',
  'command_status_check',
  'search_web',
  'fetch_web',
  'parse_figma',
  'get_figma_preview_image',
  'check_project_start_status',
  'browser_action',
  'test_case',
  'project_preview',
  'command_status_check',
  'save_memory',
  'search_memory',
  'get_memory',
  'search_spec',
  'write_todo',
  'read_todo'
] as const;

// Converts array of tool call names into a union type ("execute_command" | "read_file" | ...)
export type ToolUseName = (typeof toolUseNames)[number];

export const toolParamNames = [
  'command',
  'stage',
  'current_phase',
  'next_phase',
  'is_background',
  'requires_approval',
  'target_file',
  'code_edit',
  'instructions',
  'query',
  'target_directories',
  'description',
  'path',
  'should_read_entire_file',
  'end_line_one_indexed_inclusive',
  'start_line_one_indexed',
  'content',
  'diff',
  'regex',
  'file_pattern',
  'recursive',
  'action',
  'url',
  'preview_url',
  'coordinate',
  'text',
  'tool_name',
  'arguments',
  'question',
  'response',
  'result',
  'language',
  'server_name',
  'search',
  'replace',
  'replacements',
  'start_line',
  'end_line',
  'line_count',
  'check_duration',
  'ignore_output',
  'todos',
  'completedTodoId',
  'hl',
  'gl',
  'nocache',
  'mem_type',
  'size',
  'from',
  'type',
  'summary',
  'tags',
  'metadata',
  'memory_id'
] as const;

export type ToolParamName = (typeof toolParamNames)[number];

export interface ExecuteCommandToolUse extends ToolUse {
  name: 'execute_command';
  // Pick<Record<ToolParamName, string>, "command"> makes "command" required, but Partial<> makes it optional
  params: Partial<
    Pick<Record<ToolParamName, string>, 'command' | 'is_background' | 'requires_approval' | 'ignore_output'>
  >;
}

export interface ReadFileToolUse extends ToolUse {
  name: 'read_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'path'>>;
}

export interface EditFileToolUse extends ToolUse {
  name: 'edit_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'content' | 'instructions' | 'language' | 'path'>>;
}

export interface SearchAndReplaceToolUse extends ToolUse {
  name: 'replace_in_file';
  params: Required<Pick<Record<ToolParamName, string>, 'path' | 'replacements'>>;
}
export interface WriteToFileToolUse extends ToolUse {
  name: 'write_to_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'content' | 'line_count'>>;
}

export interface CodebaseSearchFilesToolUse extends ToolUse {
  name: 'codebase_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'query' | 'target_directories'>>;
}
export interface GrepSearchFilesToolUse extends ToolUse {
  name: 'grep_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'regex' | 'file_pattern'>>;
}

export interface ListFilesToolUse extends ToolUse {
  name: 'list_files';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'recursive'>>;
}

export interface AskFollowupQuestionToolUse extends ToolUse {
  name: 'ask_followup_question';
  params: Partial<Pick<Record<ToolParamName, string>, 'question'>>;
}

export interface ParseFigmaUrlToolUse extends ToolUse {
  name: 'parse_figma';
  params: Partial<Pick<Record<ToolParamName, string>, 'url'>>;
}

export interface GetFigmaPreviewImageToolUse extends ToolUse {
  name: 'get_figma_preview_image';
  params: Partial<Pick<Record<ToolParamName, string>, 'url'>>;
}

export interface CommandStatusCheckToolUse extends ToolUse {
  name: 'command_status_check';
  params: Partial<Pick<Record<ToolParamName, string>, 'check_duration'>>;
}

export interface SearchWebToolUse extends ToolUse {
  name: 'search_web';
  params: Partial<Pick<Record<ToolParamName, string>, 'query' | 'hl' | 'gl'>>;
}

export interface FetchWebToolUse extends ToolUse {
  name: 'fetch_web';
  params: Partial<Pick<Record<ToolParamName, string>, 'url' | 'nocache'>>;
}

export interface SearchMemoryToolUse extends ToolUse {
  name: 'search_memory';
  params: Partial<Pick<Record<ToolParamName, string>, 'mem_type' | 'text' | 'size' | 'from'>>;
}

export interface SaveMemoryToolUse extends ToolUse {
  name: 'save_memory';
  params: Partial<Pick<Record<ToolParamName, string>, 'content' | 'type' | 'summary' | 'tags' | 'metadata'>>;
}

export interface GetMemoryToolUse extends ToolUse {
  name: 'get_memory';
  params: Partial<Pick<Record<ToolParamName, string>, 'memory_id'>>;
}

export interface SearchSpecToolUse extends ToolUse {
  name: 'search_spec';
  params: Partial<Pick<Record<ToolParamName, string>, 'text' | 'size'>>;
}
