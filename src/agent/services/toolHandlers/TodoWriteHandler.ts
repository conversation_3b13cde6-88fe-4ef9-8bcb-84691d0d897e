import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON>el<PERSON>, ToolHandlerContext, <PERSON>lHandler } from '../ToolHelpers';
import {
  TextBlockParamVersion1,
  SayTool,
  EditFileRequest,
  EditFileResponse,
  TodoItem,
  TodoWriteContent
} from '../../types/type';
import { MessageService } from '../MessageService';
import { MessageParam, LocalMessage } from '../../types/type';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import delay from 'delay';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 从 LLM 传入的参数类型 可能是需要 JSON.parse 的 string
 */
type TodoParametersFromLlm = Pick<TodoItem, 'id' | 'content' | 'description'>[] | string;

/**
 * 编辑文件工具处理器
 */
export class TodoWriteHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  updateTodoStatus(todos: TodoItem[], { completedTodoId }: { completedTodoId: string }): TodoItem[] {
    const completedTodoIndex = todos.findIndex((todo) => todo.id === completedTodoId);
    if (completedTodoIndex === -1) {
      throw new Error(`Todo with id ${completedTodoId} not found`);
    }
    if (todos[completedTodoIndex].status === 'completed') {
      throw new Error(`Todo with id ${completedTodoId} is already completed`);
    }
    if (completedTodoIndex > 1 && todos[completedTodoIndex - 1].status !== 'completed') {
      throw new Error(`You should finish the previous task before updating the status of ${completedTodoId}`);
    }
    todos[completedTodoIndex].status = 'completed';
    return todos;
  }

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    beforeWriteTodo(this.context.stateManager.apiConversationHistory, this.context.stateManager.localMessages, this.context.messageService.say);
    const rawTodos = block.params.todos as TodoParametersFromLlm | undefined;

    const completedTodoId: string | undefined = block.params.completedTodoId;

    try {
      const sharedMessageProps: SayTool = {
        tool: 'writeTodo'
      };

      if (block.partial) {
        const partialMessage = JSON.stringify(sharedMessageProps);
        await this.context.messageService.say('tool', partialMessage, block.partial, undefined);
        return;
      } else {
        const todos = rawTodos
          ? typeof rawTodos === 'string'
            ? (JSON.parse(rawTodos) as Exclude<TodoParametersFromLlm, string>)
            : rawTodos
          : undefined;
        if (todos && completedTodoId) {
          // 不允许同时传入
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateInvalidParamError(
              block.name,
              'Either pass `todos` to write specific tasks or `completedTodoId` to update task status',
              this.context
            ),
            this.context.stateManager
          );
          return;
        }
        // ACTION 阶段不允许 write todo
        // RESEARCH 阶段不允许调用 completedTodoId
        // 调用 completedTodoId 需要检查之前 todo 状态
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        // 找到上一条 todoWrite 结果
        const previousTodoMessage = [...this.context.stateManager.localMessages]
          .reverse()
          .find(
            (v) => !v.partial && v.type === 'say' && v.say === 'tool' && JSON.parse(v.text || '{}').tool === 'writeTodo'
          );
        const previousTodo: TodoWriteContent | undefined = previousTodoMessage
          ? JSON.parse(JSON.parse(previousTodoMessage.text || '{}').content)
          : undefined;

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'writeTodo'
        });

        let todoToolMessageContent: TodoWriteContent | undefined;

        if (todos) {
          todoToolMessageContent = {
            type: 'write',
            todos: todos.map((todo) => ({
              ...todo,
              status: 'pending'
            }))
          };
        } else if (completedTodoId) {
          if (!previousTodo || !previousTodo.todos) {
            this.context.stateManager.updateState({
              consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
            });
            ToolHelpers.pushToolResult(
              block,
              userMessageContent,
              await ToolHelpers.sayAndCreateInvalidParamError(
                block.name,
                'No previous written Todos found',
                this.context
              ),
              this.context.stateManager
            );
            return;
          }
          try {
            todoToolMessageContent = {
              type: 'update',
              todos: this.updateTodoStatus(previousTodo!.todos || [], { completedTodoId })
            };
          } catch (error) {
            this.context.stateManager.updateState({
              consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
            });
            ToolHelpers.pushToolResult(
              block,
              userMessageContent,
              await ToolHelpers.sayAndCreateInvalidParamError(
                block.name,
                error instanceof Error ? error.message : String(error),
                this.context
              ),
              this.context.stateManager
            );
            return;
          }
        }

        const completeMessage = {
          ...sharedMessageProps,
          content: JSON.stringify(todoToolMessageContent)
        } satisfies SayTool;

        await this.context.messageService.say('tool', JSON.stringify(completeMessage), block.partial, undefined);
        await delay(3_500);

        const toolLog = ToolHelpers.generateToolLog('writeTodo', this.context.loggerManager);
        toolLog.start(JSON.stringify(completeMessage));
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            todos,
            completedTodoId
          },
          metadata: {
            name: block.name
          }
        });
        // 调用edit_file工具
        const startToolTime = Date.now();
        toolLog.end('');
        generationCall?.end();
        // TODO 工具调用 result
        const result = 'OK';
        ToolHelpers.pushToolResult(block, userMessageContent, result, this.context.stateManager);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          todos,
          completedTodoId
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'todo write',
        error,
        'write_todo'
      );
      return;
    }
  }
}

// 检查 历史消息中是否有调用 writeTodo工具
function hasTodoWriteMessage(messages: MessageParam[]): boolean {

  return !!messages.filter(
    (mes) =>
      Array.isArray(mes.content) &&
      !!mes.content.filter((e) => (e as unknown as { toolName: string }).toolName === 'write_todo').length
  ).length;
}

async function beforeWriteTodo(messages: MessageParam[], localMessages: LocalMessage[], say: MessageService['say']) {
  if (!hasTodoWriteMessage(messages)) {
    // 检查是否发送过 Research end，没有的话要发一个
    const researchEndMessages = localMessages.filter((message) => message.say === 'research_end');
    if (!researchEndMessages.length) {
      await say('research_end', '');
    }
  }
}

