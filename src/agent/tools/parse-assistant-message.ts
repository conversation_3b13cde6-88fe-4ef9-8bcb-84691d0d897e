import {
  AssistantMessageContent,
  TextContent,
  ToolUse,
  ToolParamName,
  toolParamNames,
  toolUseNames,
  ToolUseName
} from '../types/message';
import { parse } from 'best-effort-json-parser';

import {
  Chat,
  ImageBlockParam,
  MessageParam,
  TextBlockParamToolSection,
  TextBlockParamVersion0,
  TextBlockParamVersion1,
  ToolUseType
} from '../types/type.d';
import { Logger } from '@/util/log';

const logger = new Logger('parse-assistant-message');

const TypeRoleMap = {
  text: 'assistant',
  tool_use: 'tool',
  tool_result: 'user'
};

export function parseAssistantMessage(assistantMessage: string) {
  let contentBlocks: AssistantMessageContent[] = [];
  let currentTextContent: TextContent | undefined = undefined;
  let currentTextContentStartIndex = 0;
  let currentToolUse: ToolUse | undefined = undefined;
  let currentToolUseStartIndex = 0;
  let currentParamName: ToolParamName | undefined = undefined;
  let currentParamValueStartIndex = 0;
  let accumulator = '';
  let inThinkingBlock = false; // 添加标记是否在thinking块内

  // 添加过滤thinking标签的函数
  function filterThinkingContent(text: string): string | null {
    if (!text || text.indexOf('<thinking>') === -1) {
      return text;
    }

    let result = '';
    let currentPosition = 0;

    // 寻找并移除所有thinking块
    while (currentPosition < text.length) {
      const thinkingStart = text.indexOf('<thinking>', currentPosition);

      if (thinkingStart === -1) {
        // 没有更多thinking块，添加剩余文本
        result += text.substring(currentPosition);
        break;
      }

      // 添加thinking开始标签之前的内容
      if (thinkingStart > currentPosition) {
        result += text.substring(currentPosition, thinkingStart);
      }

      // 寻找此thinking块的结束位置
      const thinkingEnd = text.indexOf('</thinking>', thinkingStart);

      if (thinkingEnd === -1) {
        // 未闭合的thinking块，不添加剩余内容
        break;
      }

      // 移动位置到thinking块之后
      currentPosition = thinkingEnd + '</thinking>'.length;
    }

    // 如果过滤后没有剩余内容，返回null
    return result.trim().length > 0 ? result.trim() : null;
  }

  for (let i = 0; i < assistantMessage.length; i++) {
    const char = assistantMessage[i];
    accumulator += char;

    // 检查是否进入或离开thinking块
    if (accumulator.endsWith('<thinking>')) {
      inThinkingBlock = true;
      // 如果之前有文本内容，先保存它
      if (currentTextContent) {
        const content = accumulator.slice(currentTextContentStartIndex, i - '<thinking>'.length).trim();
        if (content) {
          currentTextContent.content = content;
          currentTextContent.partial = false;
          contentBlocks.push(currentTextContent);
        }
        currentTextContent = undefined;
      }
      continue;
    }
    if (accumulator.endsWith('</thinking>')) {
      inThinkingBlock = false;
      currentTextContentStartIndex = i + 1;
      continue;
    }

    // 如果在thinking块内，跳过处理
    if (inThinkingBlock) {
      continue;
    }

    // there should not be a param without a tool use
    if (currentToolUse && currentParamName) {
      const currentParamValue = accumulator.slice(currentParamValueStartIndex);
      const paramClosingTag = `</${currentParamName}>`;
      if (currentParamValue.endsWith(paramClosingTag)) {
        // end of param value
        currentToolUse.params[currentParamName] = currentParamValue.slice(0, -paramClosingTag.length).trim();
        currentParamName = undefined;
        continue;
      } else {
        // partial param value is accumulating
        continue;
      }
    }

    // no currentParamName

    if (currentToolUse) {
      const currentToolValue = accumulator.slice(currentToolUseStartIndex);
      const toolUseClosingTag = `</${currentToolUse.name}>`;
      if (currentToolValue.endsWith(toolUseClosingTag)) {
        // end of a tool use
        currentToolUse.partial = false;
        contentBlocks.push(currentToolUse);
        currentToolUse = undefined;
        continue;
      } else {
        const possibleParamOpeningTags = toolParamNames.map((name) => `<${name}>`);
        for (const paramOpeningTag of possibleParamOpeningTags) {
          if (accumulator.endsWith(paramOpeningTag)) {
            // start of a new parameter
            currentParamName = paramOpeningTag.slice(1, -1) as ToolParamName;
            currentParamValueStartIndex = accumulator.length;
            break;
          }
        }

        // there's no current param, and not starting a new param

        // special case for write_to_file where file contents could contain the closing tag, in which case the param would have closed and we end up with the rest of the file contents here. To work around this, we get the string between the starting content tag and the LAST content tag.
        const contentParamName: ToolParamName = 'content';
        if (currentToolUse.name === 'edit_file' && accumulator.endsWith(`</${contentParamName}>`)) {
          const toolContent = accumulator.slice(currentToolUseStartIndex);
          const contentStartTag = `<${contentParamName}>`;
          const contentEndTag = `</${contentParamName}>`;
          const contentStartIndex = toolContent.indexOf(contentStartTag) + contentStartTag.length;
          const contentEndIndex = toolContent.lastIndexOf(contentEndTag);
          if (contentStartIndex !== -1 && contentEndIndex !== -1 && contentEndIndex > contentStartIndex) {
            currentToolUse.params[contentParamName] = toolContent.slice(contentStartIndex, contentEndIndex).trim();
          }
        }

        // partial tool value is accumulating
        continue;
      }
    }

    // no currentToolUse

    let didStartToolUse = false;
    const possibleToolUseOpeningTags = toolUseNames.map((name) => `<${name}>`);
    for (const toolUseOpeningTag of possibleToolUseOpeningTags) {
      if (accumulator.endsWith(toolUseOpeningTag)) {
        // start of a new tool use
        currentToolUse = {
          type: ToolUseType.TOOL_USE,
          name: toolUseOpeningTag.slice(1, -1) as ToolUseName,
          params: {},
          partial: true
        };
        currentToolUseStartIndex = accumulator.length;
        // this also indicates the end of the current text content
        if (currentTextContent) {
          currentTextContent.partial = false;
          // remove the partially accumulated tool use tag from the end of text (<tool)
          let content = currentTextContent.content.slice(0, -toolUseOpeningTag.slice(0, -1).length).trim();

          // 过滤thinking标签及内容
          const filteredContent = filterThinkingContent(content);
          if (filteredContent) {
            currentTextContent.content = filteredContent;
            contentBlocks.push(currentTextContent);
          }
          currentTextContent = undefined;
        }

        didStartToolUse = true;
        break;
      }
    }

    if (!didStartToolUse) {
      // no tool use, so it must be text either at the beginning or between tools
      if (currentTextContent === undefined) {
        currentTextContentStartIndex = i;
      }

      // 获取当前文本并过滤thinking内容
      const currentText = accumulator.slice(currentTextContentStartIndex).trim();
      const filteredText = filterThinkingContent(currentText);

      // 只有在过滤后还有内容时才创建TextContent
      if (filteredText) {
        currentTextContent = {
          type: 'text',
          content: filteredText,
          partial: true
        };
      } else {
        // 如果过滤后没有内容，仍然创建一个TextContent但使用空字符串
        // currentTextContent = {
        //   type: "text",
        //   content: "",
        //   partial: true,
        // };
        currentTextContent = undefined;
      }
    }
  }

  if (currentToolUse) {
    // stream did not complete tool call, add it as partial
    if (currentParamName) {
      // tool call has a parameter that was not completed
      currentToolUse.params[currentParamName] = accumulator.slice(currentParamValueStartIndex).trim();
    }
    contentBlocks.push(currentToolUse);
  }

  // Note: it doesnt matter if check for currentToolUse or currentTextContent, only one of them will be defined since only one can be partial at a time
  if (currentTextContent) {
    // 过滤最终的文本内容中的thinking标签
    const filteredContent = filterThinkingContent(currentTextContent.content);
    if (filteredContent) {
      currentTextContent.content = filteredContent;
      // stream did not complete text content, add it as partial
      contentBlocks.push(currentTextContent);
    }
  }

  return contentBlocks;
}

/**
 * 创建新的文本内容
 */
function createTextContent(content: string): TextContent {
  return {
    type: 'text',
    content,
    partial: true
  };
}

/**
 * 解析工具参数字符串为对象
 */
function parseToolParams(toolUse: ToolUse): void {
  try {
    if (toolUse.paramsString) {
      toolUse.params = JSON.parse(toolUse.paramsString);
      toolUse.paramsString = '';
    }
  } catch (error) {
    logger.error(`Failed to parse tool use params: ${toolUse.paramsString}`);
    toolUse.params = {};
  }
}

/**
 * 处理助手文本消息
 */
function handleAssistantTextMessage(message: Chat.AgentChatMessage, lastItem: TextContent): void {
  if (message.content) {
    if (typeof message.content === 'string') {
      lastItem.content += message.content;
    } else if (Array.isArray(message.content)) {
      for (const content of message.content) {
        if (content.type === 'text' && content.text) {
          lastItem.content += content.text;
        } else if (content.type === 'image' && content.source?.url) {
          lastItem.content += content.source.url;
        }
      }
    }
  }
}

/**
 * 创建工具使用对象的工厂函数
 */
function createToolUse(toolCall: Chat.ToolCall): ToolUse {
  return {
    type: ToolUseType.TOOL_USE,
    id: toolCall.id,
    name: toolCall.function.name as ToolUseName,
    params: {},
    paramsString: toolCall.function.arguments,
    partial: true
  };
}

/**
 * 处理新的工具调用消息
 */
function handleNewToolMessage(message: Chat.AgentChatMessage, assistantMessage: AssistantMessageContent[]): void {
  const toolCalls = message.tool_calls;

  if (!toolCalls || toolCalls.length === 0) {
    return;
  }

  for (const toolCall of toolCalls) {
    const newTool = createToolUse(toolCall);
    assistantMessage.push(newTool);
  }
}

/**
 * 处理工具调用消息
 */
function handleToolMessage(
  message: Chat.AgentChatMessage,
  assistantMessage: AssistantMessageContent[],
  lastItem: ToolUse
): void {
  const toolCalls = message.tool_calls;

  if (!toolCalls || toolCalls.length === 0) {
    return;
  }

  for (const toolCall of toolCalls) {
    if (toolCall.id === lastItem.id) {
      // 更新现有工具调用
      lastItem.partial = true;
      lastItem.name = toolCall.function.name
        ? toolCall.function.name === lastItem.name
          ? toolCall.function.name
          : ((lastItem.name + toolCall.function.name) as ToolUseName)
        : lastItem.name;
      lastItem.paramsString += toolCall.function.arguments;
      try {
        const trimmedParams = lastItem.paramsString?.trim();
        if (trimmedParams) {
          if (trimmedParams.endsWith('}') || trimmedParams.endsWith(']')) {
            lastItem.params = JSON.parse(lastItem.paramsString!);
          } else {
            lastItem.params = parse(lastItem.paramsString!);
          }
        }
      } catch (_) { }
    } else {
      // 创建新的工具调用
      const newTool = createToolUse(toolCall);
      assistantMessage.push(newTool);

      // 完成前一个工具调用
      lastItem.partial = false;
      parseToolParams(lastItem);
    }
  }
}

export function formatAssistantMessage(
  message: Chat.AgentChatMessage,
  assistantMessage: AssistantMessageContent[] = []
): AssistantMessageContent[] {
  // 边界条件检查
  if (!message) {
    logger.warn('Invalid input parameters for formatAssistantMessage');
    return assistantMessage;
  }

  const lastItem = assistantMessage[assistantMessage.length - 1];
  let isSameRole = lastItem ? TypeRoleMap[lastItem.type] === message.role : false;
  if (!isSameRole) {
    if (message.tool_calls && message.tool_calls.length > 0 && lastItem) {
      isSameRole = true;
    }
  }

  if (isSameRole) {
    // 相同角色：更新现有消息
    if (message.tool_calls && message.tool_calls.length > 0) {
      handleToolMessage(message, assistantMessage, lastItem as ToolUse);
    } else {
      if (lastItem.type === 'text') {
        handleAssistantTextMessage(message, lastItem);
      } else {
        logger.error(`Unexpected message role: ${message.role}`);
      }
    }
  } else {
    // 不同角色：完成上一个消息，创建新消息
    if (lastItem) {
      lastItem.partial = false;
    }

    if (message.tool_calls && message.tool_calls.length > 0) {
      handleNewToolMessage(message, assistantMessage);
    } else {
      if (message.content) {
        if (typeof message.content === 'string') {
          const textContent = createTextContent(message.content);
          assistantMessage.push(textContent);
        } else if (Array.isArray(message.content)) {
          for (const content of message.content) {
            if (content.type === 'text' && content.text) {
              const imageContent = createTextContent(content.text!);
              assistantMessage.push(imageContent);
            } else if (content.type === 'image' && content.source?.url) {
              const imageContent = createTextContent(content.source.url);
              assistantMessage.push(imageContent);
            }
          }
        }
      }
    }
  }

  return assistantMessage;
}

export function formatLlmMessages(messages: MessageParam[]) {
  const llmMessages: Chat.AgentChatMessage[] = [];

  for (const message of messages) {
    if (message.role === 'user') {
      const content = message.content;
      if (typeof content === 'string') {
        llmMessages.push({
          content: [
            {
              type: 'text',
              text: content
            }
          ],
          role: message.role,
          chatId: message.chatId
        });
      } else {
        const isContainTool = content.some((item) => 'toolId' in item && item.toolId);
        if (!isContainTool) {
          const c: Chat.AgentChatMessage['content'] = [];

          content.forEach((item) => {
            if (item.type === 'text') {
              c.push({
                type: 'text',
                text: item.text
              });
            } else if (item.type === 'image') {
              c.push({
                type: 'image',
                source: {
                  type: 'url',
                  url: item.source.url
                }
              });
            }
          });
          llmMessages.push({
            content: c,
            role: message.role,
            chatId: message.chatId
          });
        } else {
          // v1 的情况下做兼容，判断是否有 toolid，如果有，则使用新的，如果没有，则使用旧的。多条消息的情况下，将多条消息合并成一条。
          const items: ((TextBlockParamVersion0 | ImageBlockParam) & TextBlockParamToolSection)[] = (
            content as TextBlockParamVersion1[]
          ).filter(
            (c: TextBlockParamVersion1) =>
              c.category === 'tool-response' ||
              c.category === 'tool-title' ||
              c.category === 'tool-feedback' ||
              c.category === 'tool-exception'
          ) as ((TextBlockParamVersion0 | ImageBlockParam) & TextBlockParamToolSection)[];

          if (items.length) {
            const firstToolItem = items[0];
            const toolId = firstToolItem.toolId!;

            const isMcpTool = firstToolItem.toolName === 'use_mcp_tool';
            let params = firstToolItem.params;
            const toolName = isMcpTool ? params.server_name || '' : firstToolItem.toolName;
            if (isMcpTool) {
              params = JSON.parse(params.arguments || '{}');
            }
            let lastMessage: Chat.AgentChatMessage = {
              role: 'assistant',
              chatId: message.chatId,
              content: []
            };
            // 从后往前找到第一个assistant消息, 为了兼容多 tool 的情况，多 tool 的话最后一条消息可能是 tool 消息
            for (let i = llmMessages.length - 1; i >= 0; i--) {
              lastMessage = llmMessages[i];
              if (lastMessage.role === 'assistant') {
                break;
              }
            }

            if (lastMessage) {
              if (!lastMessage.tool_calls) {
                lastMessage.tool_calls = [];
              }
              lastMessage.tool_calls?.push({
                id: toolId,
                index: 0,
                type: 'function',
                function: {
                  name: toolName,
                  arguments: JSON.stringify(params)
                }
              });
            } else {
              llmMessages.push({
                role: 'assistant',
                chatId: message.chatId,
                content: [],
                tool_calls: [
                  {
                    id: toolId,
                    index: 0,
                    type: 'function',
                    function: {
                      name: toolName,
                      arguments: JSON.stringify(params)
                    }
                  }
                ]
              });
            }

            let content = '';
            // 工具返回的图片
            let imageContent: ImageBlockParam[] = [];
            for (const c of items) {
              if (c?.type === 'image') {
                // 新增 tool 工具返回图片能力
                if (c.source.url) {
                  imageContent.push({
                    type: 'image',
                    source: {
                      type: 'url',
                      url: c.source.url
                    }
                  });
                }
                continue;
              }
              content += c.text;
            }
            const toolResult: Chat.AgentChatMessage = {
              role: 'tool',
              tool_call_id: toolId,
              chatId: message.chatId,
              content: [
                {
                  type: 'text',
                  text: content
                },
                ...imageContent,
              ]
            };
            llmMessages.push(toolResult);
          }
        }
      }
    } else {
      const content = message.content;
      if (typeof content === 'string') {
        llmMessages.push({
          content: [
            {
              type: 'text',
              text: content
            }
          ],
          role: message.role,
          chatId: message.chatId
        });
      } else {
        const c: Chat.AgentChatMessage['content'] = [];
        content.forEach((item) => {
          if (item.type === 'text') {
            c.push({
              type: 'text',
              text: item.text
            });
          } else if (item.type === 'image') {
            c.push({
              type: 'image',
              source: {
                type: 'url',
                url: item.source.url
              }
            });
          }
        });
        llmMessages.push({
          content: c,
          role: message.role,
          chatId: message.chatId
        });
      }
    }
  }

  /** 确保没有空消息 */
  const filteredMessages: Chat.AgentChatMessage[] = [];
  for (const message of llmMessages) {
    if (message.role === 'assistant' || message.role === 'user') {
      if (typeof message.content === 'string' && message.content) {
        filteredMessages.push(message);
      } else if (Array.isArray(message.content)) {
        message.content = message.content.filter(
          (item) => (item.type === 'text' && item.text) || (item.type === 'image' && item.source.url)
        );
        if (message.content.length || message.tool_calls?.length) {
          filteredMessages.push(message);
        }
      }
    } else if (message.role === 'tool' && message.tool_call_id) {
      filteredMessages.push(message);
    } else {
      filteredMessages.push(message);
    }
  }

  /** 确保工具是成对出现的 */
  const pairedMessages: Chat.AgentChatMessage[] = [];
  const toolCallIds = new Set<string>();
  const toolResponseIds = new Set<string>();

  // 收集所有的工具调用ID和工具回复ID
  for (const message of filteredMessages) {
    if (message.role === 'assistant' && message.tool_calls) {
      for (const toolCall of message.tool_calls) {
        toolCallIds.add(toolCall.id);
      }
    } else if (message.role === 'tool' && message.tool_call_id) {
      toolResponseIds.add(message.tool_call_id);
    }
  }

  // 检查工具调用和回复是否成对
  for (const message of filteredMessages) {
    if (message.role === 'assistant' && message.tool_calls) {
      // 检查这个assistant消息的所有工具调用是否都有对应的回复
      const hasAllResponses = message.tool_calls.every((toolCall) => toolResponseIds.has(toolCall.id));

      if (hasAllResponses) {
        pairedMessages.push(message);
      } else {
        // 如果没有对应的回复，移除工具调用，只保留文本内容
        const messageWithoutToolCalls = { ...message };
        delete messageWithoutToolCalls.tool_calls;
        if (messageWithoutToolCalls.content && messageWithoutToolCalls.content.length > 0) {
          pairedMessages.push(messageWithoutToolCalls);
        }
      }
    } else if (message.role === 'tool') {
      // 只添加有对应工具调用的工具回复
      if (message.tool_call_id && toolCallIds.has(message.tool_call_id)) {
        pairedMessages.push(message);
      }
    } else {
      // 其他类型的消息直接添加
      pairedMessages.push(message);
    }
  }

  return pairedMessages;
}
